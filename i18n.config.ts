import { PrismaClient } from './generated/prisma/index.js';

const prisma = new PrismaClient();

// Allowed locales (must match what you support in your app)
const allowedLocales = ['en', 'mn', 'ch'] as const;
type LocaleType = typeof allowedLocales[number];

export default defineI18nConfig(async () => {
  let defaultLocale: LocaleType = 'en';

  try {
    const locales = await prisma.translation.findMany({
      select: { locale: true },
      distinct: ['locale']
    });

    console.log('Locales from DB:', locales);

    const validLocaleEntry = locales.find(l =>
      allowedLocales.includes(l.locale as LocaleType)
    );

    if (validLocaleEntry && validLocaleEntry.locale) {
      defaultLocale = validLocaleEntry.locale as LocaleType;
    } else {
      console.warn('No valid locale found in DB; falling back to default.');
    }
  } catch (error) {
    console.warn('Failed to fetch locale from database, using default:', error);
  }

  // Ensure TypeScript is happy with the messages object
  return {
    legacy: false,
    locale: defaultLocale,
    fallbackLocale: 'en',
    messages: {
      en: {},
      mn: {},
      ch: {}
    },
    silentTranslationWarn: true,
    silentFallbackWarn: true,
    missingWarn: false,
    fallbackWarn: false
  };
});


import { PrismaClient } from './generated/prisma/index.js';

const prisma = new PrismaClient();

// Define the allowed locales for TS to be happy
const allowedLocales = ['en', 'mn', 'ch'] as const;
type LocaleType = typeof allowedLocales[number];

export default defineI18nConfig(async () => {
  let defaultLocale: LocaleType = 'en';

  try {
    const locales = await prisma.translation.findMany({
      select: { locale: true },
      distinct: ['locale']
    });

    const validLocale = locales.find(l => allowedLocales.includes(l.locale as LocaleType));
    if (validLocale) {
      defaultLocale = validLocale.locale as LocaleType;
    }
  } catch (error) {
    console.warn('Failed to fetch locale from database, using default');
  }

  return {
    legacy: false,
    locale: defaultLocale,
    fallbackLocale: 'en',
    messages: {
      en: {},
      mn: {},
      ch: {}
    },
    
    silentTranslationWarn: true,
    silentFallbackWarn: true,
    missingWarn: false,
    fallbackWarn: false
  };
});
